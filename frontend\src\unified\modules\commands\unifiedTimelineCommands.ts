import { generateCommandId } from '../../../utils/idGenerator'
import type { Ref } from 'vue'
import type { SimpleCommand } from '../../../types'
import type { UnifiedTimelineItemData, TransformData } from '../../timelineitem/TimelineItemData'
import type { UnifiedMediaItemData } from '../../mediaitem/types'

/**
 * 统一选择时间轴项目命令
 * 支持统一时间轴项目的单选和多选操作的撤销/重做
 * 记录选择状态的变化，支持恢复到之前的选择状态
 */
export class UnifiedSelectTimelineItemsCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string
  private previousSelection: Set<string> // 保存操作前的选择状态
  private newSelection: Set<string> // 保存操作后的选择状态

  constructor(
    private itemIds: string[],
    private mode: 'replace' | 'toggle',
    private selectionModule: {
      selectedTimelineItemIds: Ref<Set<string>>
      selectTimelineItems: (itemIds: string[], mode: 'replace' | 'toggle') => void
      syncAVCanvasSelection: () => void
    },
    private timelineModule: {
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    private mediaModule: {
      getMediaItem: (id: string) => UnifiedMediaItemData | undefined
    },
  ) {
    this.id = generateCommandId()

    // 保存当前选择状态
    this.previousSelection = new Set(this.selectionModule.selectedTimelineItemIds.value)

    // 计算新的选择状态
    this.newSelection = this.calculateNewSelection()

    // 生成描述信息
    this.description = this.generateDescription()

    console.log('💾 保存选择操作数据:', {
      itemIds,
      mode,
      previousSelection: Array.from(this.previousSelection),
      newSelection: Array.from(this.newSelection),
    })
  }

  /**
   * 计算新的选择状态
   */
  private calculateNewSelection(): Set<string> {
    const newSelection = new Set(this.previousSelection)

    if (this.mode === 'replace') {
      // 替换模式：清空现有选择，设置新选择
      newSelection.clear()
      this.itemIds.forEach((id) => newSelection.add(id))
    } else {
      // 切换模式：切换每个项目的选择状态
      this.itemIds.forEach((id) => {
        if (newSelection.has(id)) {
          newSelection.delete(id)
        } else {
          newSelection.add(id)
        }
      })
    }

    return newSelection
  }

  /**
   * 生成描述信息
   */
  private generateDescription(): string {
    // 单选/多选/取消选择的不同描述
    if (this.newSelection.size === 0) {
      return '取消选择'
    } else if (this.newSelection.size === 1) {
      // 单选：显示项目名称
      const itemId = Array.from(this.newSelection)[0]
      const timelineItem = this.timelineModule.getTimelineItem(itemId)
      
      if (timelineItem) {
        const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
        const itemName = mediaItem?.name || '未知素材'
        return `选择项目: ${itemName}`
      }
      return '选择单个项目'
    } else {
      // 多选：显示数量
      return `选择 ${this.newSelection.size} 个项目`
    }
  }

  /**
   * 执行命令：应用新的选择状态
   */
  async execute(): Promise<void> {
    try {
      console.log(`🔄 执行选择操作: ${this.description}`)

      // 直接设置选择状态，避免触发新的历史记录
      this.applySelection(this.newSelection)

      console.log(`✅ 选择操作完成: ${Array.from(this.newSelection).length} 个项目被选中`)
    } catch (error) {
      console.error(`❌ 选择操作失败: ${this.description}`, error)
      throw error
    }
  }

  /**
   * 撤销命令：恢复到之前的选择状态
   */
  async undo(): Promise<void> {
    try {
      console.log(`🔄 撤销选择操作: ${this.description}`)

      // 恢复到之前的选择状态
      this.applySelection(this.previousSelection)

      console.log(`↩️ 已恢复选择状态: ${Array.from(this.previousSelection).length} 个项目被选中`)
    } catch (error) {
      console.error(`❌ 撤销选择操作失败: ${this.description}`, error)
      throw error
    }
  }

  /**
   * 应用选择状态（不触发历史记录）
   */
  private applySelection(selection: Set<string>): void {
    // 直接更新选择状态，不通过selectTimelineItems方法以避免循环调用
    this.selectionModule.selectedTimelineItemIds.value.clear()
    selection.forEach((id) => this.selectionModule.selectedTimelineItemIds.value.add(id))

    // 手动触发AVCanvas同步逻辑
    this.selectionModule.syncAVCanvasSelection()
  }
}

// ==================== 添加时间轴项目命令 ====================

/**
 * 统一添加时间轴项目命令
 * 支持添加统一时间轴项目的撤销/重做操作
 * 适配新架构的统一类型系统
 */
export class UnifiedAddTimelineItemCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string
  private originalTimelineItemData: UnifiedTimelineItemData | null = null

  constructor(
    timelineItem: UnifiedTimelineItemData,
    private timelineModule: {
      addTimelineItem: (item: UnifiedTimelineItemData) => void
      removeTimelineItem: (id: string) => void
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    private mediaModule: {
      getMediaItem: (id: string) => UnifiedMediaItemData | undefined
    },
    private webavModule?: {
      addSprite: (sprite: any) => Promise<boolean>
      removeSprite: (sprite: any) => boolean
    },
  ) {
    this.id = generateCommandId()

    const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
    this.description = `添加时间轴项目: ${mediaItem?.name || '未知素材'}`

    // 保存原始数据用于重建
    this.originalTimelineItemData = { ...timelineItem }
  }

  /**
   * 执行命令：添加时间轴项目
   */
  async execute(): Promise<void> {
    try {
      if (!this.originalTimelineItemData) {
        throw new Error('时间轴项目数据不存在')
      }

      console.log(`🔄 执行添加操作：添加统一时间轴项目...`)

      // 1. 添加到时间轴
      this.timelineModule.addTimelineItem(this.originalTimelineItemData)

      // 2. 如果有webav模块，添加sprite到WebAV画布
      if (this.webavModule && this.originalTimelineItemData.sprite) {
        await this.webavModule.addSprite(this.originalTimelineItemData.sprite)
      }

      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItemData.mediaItemId)
      console.log(`✅ 已添加统一时间轴项目: ${mediaItem?.name || '未知素材'}`)
    } catch (error) {
      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItemData?.mediaItemId || '')
      console.error(`❌ 添加时间轴项目失败: ${mediaItem?.name || '未知项目'}`, error)
      throw error
    }
  }

  /**
   * 撤销命令：移除时间轴项目
   */
  async undo(): Promise<void> {
    try {
      if (!this.originalTimelineItemData) {
        console.warn('⚠️ 时间轴项目数据不存在，无法撤销')
        return
      }

      const existingItem = this.timelineModule.getTimelineItem(this.originalTimelineItemData.id)
      if (!existingItem) {
        console.warn(`⚠️ 时间轴项目不存在，无法撤销: ${this.originalTimelineItemData.id}`)
        return
      }

      // 移除时间轴项目
      this.timelineModule.removeTimelineItem(this.originalTimelineItemData.id)

      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItemData.mediaItemId)
      console.log(`↩️ 已撤销添加时间轴项目: ${mediaItem?.name || '未知素材'}`)
    } catch (error) {
      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItemData?.mediaItemId || '')
      console.error(`❌ 撤销添加时间轴项目失败: ${mediaItem?.name || '未知项目'}`, error)
      throw error
    }
  }
}

// ==================== 移除时间轴项目命令 ====================

/**
 * 统一移除时间轴项目命令
 * 支持移除统一时间轴项目的撤销/重做操作
 * 适配新架构的统一类型系统
 */
export class UnifiedRemoveTimelineItemCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string
  private originalTimelineItemData: UnifiedTimelineItemData | null = null

  constructor(
    private timelineItemId: string,
    timelineItem: UnifiedTimelineItemData,
    private timelineModule: {
      addTimelineItem: (item: UnifiedTimelineItemData) => void
      removeTimelineItem: (id: string) => void
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    private mediaModule: {
      getMediaItem: (id: string) => UnifiedMediaItemData | undefined
    },
    private webavModule?: {
      addSprite: (sprite: any) => Promise<boolean>
      removeSprite: (sprite: any) => boolean
    },
  ) {
    this.id = generateCommandId()

    const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
    this.description = `移除时间轴项目: ${mediaItem?.name || '未知素材'}`

    // 保存完整的项目数据用于撤销
    this.originalTimelineItemData = { ...timelineItem }

    console.log('💾 保存删除项目的数据:', {
      id: this.originalTimelineItemData.id,
      mediaItemId: this.originalTimelineItemData.mediaItemId,
      mediaType: this.originalTimelineItemData.mediaType,
      timeRange: this.originalTimelineItemData.timeRange,
    })
  }

  /**
   * 执行命令：删除时间轴项目
   */
  async execute(): Promise<void> {
    try {
      // 检查项目是否存在
      const existingItem = this.timelineModule.getTimelineItem(this.timelineItemId)
      if (!existingItem) {
        console.warn(`⚠️ 时间轴项目不存在，无法删除: ${this.timelineItemId}`)
        return
      }

      // 删除时间轴项目
      this.timelineModule.removeTimelineItem(this.timelineItemId)

      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItemData?.mediaItemId || '')
      console.log(`🗑️ 已删除统一时间轴项目: ${mediaItem?.name || '未知素材'}`)
    } catch (error) {
      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItemData?.mediaItemId || '')
      console.error(`❌ 删除时间轴项目失败: ${mediaItem?.name || '未知项目'}`, error)
      throw error
    }
  }

  /**
   * 撤销命令：重新创建时间轴项目
   */
  async undo(): Promise<void> {
    try {
      if (!this.originalTimelineItemData) {
        throw new Error('没有有效的时间轴项目数据')
      }

      console.log(`🔄 撤销删除操作：重建统一时间轴项目...`)

      // 1. 添加到时间轴
      this.timelineModule.addTimelineItem(this.originalTimelineItemData)

      // 2. 如果有webav模块，添加sprite到WebAV画布
      if (this.webavModule && this.originalTimelineItemData.sprite) {
        await this.webavModule.addSprite(this.originalTimelineItemData.sprite)
      }

      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItemData.mediaItemId)
      console.log(`↩️ 已撤销删除统一时间轴项目: ${mediaItem?.name || '未知素材'}`)
    } catch (error) {
      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItemData?.mediaItemId || '')
      console.error(`❌ 撤销删除时间轴项目失败: ${mediaItem?.name || '未知项目'}`, error)
      throw error
    }
  }
}

// ==================== 移动时间轴项目命令 ====================

/**
 * 统一移动时间轴项目命令
 * 支持统一时间轴项目位置移动的撤销/重做操作
 * 包括时间位置移动和轨道间移动
 */
export class UnifiedMoveTimelineItemCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string

  constructor(
    private timelineItemId: string,
    private oldPositionFrames: number, // 旧的时间位置（帧数）
    private newPositionFrames: number, // 新的时间位置（帧数）
    private oldTrackId: string, // 旧的轨道ID
    private newTrackId: string, // 新的轨道ID
    private timelineModule: {
      updateTimelineItemPosition: (id: string, positionFrames: number, trackId?: string) => void
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    private mediaModule: {
      getMediaItem: (id: string) => UnifiedMediaItemData | undefined
    },
  ) {
    this.id = generateCommandId()

    const timelineItem = this.timelineModule.getTimelineItem(timelineItemId)
    let itemName = '未知素材'

    // 根据项目类型获取名称
    if (timelineItem) {
      const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
      itemName = mediaItem?.name || '未知素材'
    }

    // 生成描述信息
    const positionChanged = this.oldPositionFrames !== this.newPositionFrames
    const trackChanged = oldTrackId !== newTrackId

    if (positionChanged && trackChanged) {
      this.description = `移动时间轴项目: ${itemName} (位置: ${this.oldPositionFrames}帧→${this.newPositionFrames}帧, 轨道: ${oldTrackId}→${newTrackId})`
    } else if (positionChanged) {
      this.description = `移动时间轴项目: ${itemName} (位置: ${this.oldPositionFrames}帧→${this.newPositionFrames}帧)`
    } else if (trackChanged) {
      this.description = `移动时间轴项目: ${itemName} (轨道: ${oldTrackId}→${newTrackId})`
    } else {
      this.description = `移动时间轴项目: ${itemName} (无变化)`
    }

    console.log('💾 保存移动操作数据:', {
      timelineItemId,
      oldPositionFrames: this.oldPositionFrames,
      newPositionFrames: this.newPositionFrames,
      oldTrackId,
      newTrackId,
      positionChanged,
      trackChanged,
    })
  }

  /**
   * 执行命令：移动时间轴项目到新位置
   */
  async execute(): Promise<void> {
    try {
      // 检查项目是否存在
      const timelineItem = this.timelineModule.getTimelineItem(this.timelineItemId)
      if (!timelineItem) {
        console.warn(`⚠️ 时间轴项目不存在，无法移动: ${this.timelineItemId}`)
        return
      }

      // 移动到新位置
      const trackIdToSet = this.oldTrackId !== this.newTrackId ? this.newTrackId : undefined
      this.timelineModule.updateTimelineItemPosition(
        this.timelineItemId,
        this.newPositionFrames,
        trackIdToSet,
      )

      // 获取项目名称
      const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
      const itemName = mediaItem?.name || '未知素材'

      console.log(
        `🔄 已移动时间轴项目: ${itemName} 到位置 ${this.newPositionFrames}帧, 轨道 ${this.newTrackId}`,
      )
    } catch (error) {
      const timelineItem = this.timelineModule.getTimelineItem(this.timelineItemId)
      let itemName = '未知素材'
      if (timelineItem) {
        const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
        itemName = mediaItem?.name || '未知素材'
      }
      console.error(`❌ 移动时间轴项目失败: ${itemName}`, error)
      throw error
    }
  }

  /**
   * 撤销命令：移动时间轴项目回到原位置
   */
  async undo(): Promise<void> {
    try {
      // 检查项目是否存在
      const timelineItem = this.timelineModule.getTimelineItem(this.timelineItemId)
      if (!timelineItem) {
        console.warn(`⚠️ 时间轴项目不存在，无法撤销移动: ${this.timelineItemId}`)
        return
      }

      // 移动回原位置
      const trackIdToSet = this.oldTrackId !== this.newTrackId ? this.oldTrackId : undefined
      this.timelineModule.updateTimelineItemPosition(
        this.timelineItemId,
        this.oldPositionFrames,
        trackIdToSet,
      )

      // 获取项目名称
      const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
      const itemName = mediaItem?.name || '未知素材'

      console.log(
        `↩️ 已撤销移动时间轴项目: ${itemName} 回到位置 ${this.oldPositionFrames}帧, 轨道 ${this.oldTrackId}`,
      )
    } catch (error) {
      const timelineItem = this.timelineModule.getTimelineItem(this.timelineItemId)
      let itemName = '未知素材'
      if (timelineItem) {
        const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
        itemName = mediaItem?.name || '未知素材'
      }
      console.error(`❌ 撤销移动时间轴项目失败: ${itemName}`, error)
      throw error
    }
  }
}

// ==================== 复制时间轴项目命令 ====================

/**
 * 统一复制时间轴项目命令
 * 支持复制统一时间轴项目的撤销/重做操作
 * 适配新架构的统一类型系统
 */
export class UnifiedDuplicateTimelineItemCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string
  private originalTimelineItemData: UnifiedTimelineItemData | null = null
  public readonly newTimelineItemId: string // 新创建的项目ID

  constructor(
    originalTimelineItemId: string,
    originalTimelineItem: UnifiedTimelineItemData,
    private newPositionFrames: number, // 新项目的时间位置（帧数）
    private timelineModule: {
      addTimelineItem: (item: UnifiedTimelineItemData) => void
      removeTimelineItem: (id: string) => void
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    private mediaModule: {
      getMediaItem: (id: string) => UnifiedMediaItemData | undefined
    },
    private webavModule?: {
      addSprite: (sprite: any) => Promise<boolean>
      removeSprite: (sprite: any) => boolean
    },
  ) {
    this.id = generateCommandId()

    const mediaItem = this.mediaModule.getMediaItem(originalTimelineItem.mediaItemId)
    this.description = `复制时间轴项目: ${mediaItem?.name || '未知素材'}`

    // 保存原始项目的完整数据
    this.originalTimelineItemData = { ...originalTimelineItem }

    // 生成新项目的ID
    this.newTimelineItemId = `timeline_item_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 重建复制的时间轴项目
   */
  private rebuildDuplicatedItem(): UnifiedTimelineItemData {
    if (!this.originalTimelineItemData) {
      throw new Error('原始时间轴项目数据不存在')
    }

    console.log('🔄 [UnifiedDuplicateTimelineItemCommand] 重建复制的时间轴项目...')

    // 计算新的时间范围
    const originalTimeRange = this.originalTimelineItemData.timeRange
    const originalDurationFrames = originalTimeRange.timelineEndTime - originalTimeRange.timelineStartTime
    const newTimelineStartTimeFrames = this.newPositionFrames
    const newTimelineEndTimeFrames = newTimelineStartTimeFrames + originalDurationFrames

    // 创建新的时间轴项目
    const newTimelineItem: UnifiedTimelineItemData = {
      ...this.originalTimelineItemData,
      id: this.newTimelineItemId,
      timeRange: {
        timelineStartTime: newTimelineStartTimeFrames,
        timelineEndTime: newTimelineEndTimeFrames,
      },
      // 复制配置但不共享引用
      config: { ...this.originalTimelineItemData.config },
      // sprite需要重新创建，这里暂时设为undefined
      sprite: undefined,
    }

    console.log('✅ [UnifiedDuplicateTimelineItemCommand] 复制的时间轴项目重建完成')
    return newTimelineItem
  }

  /**
   * 执行命令：创建复制的时间轴项目
   */
  async execute(): Promise<void> {
    try {
      console.log(`🔄 执行复制操作：从源头重建时间轴项目...`)

      // 重建复制的TimelineItem
      const newTimelineItem = this.rebuildDuplicatedItem()

      // 1. 添加到时间轴
      this.timelineModule.addTimelineItem(newTimelineItem)

      // 2. 如果有webav模块，添加sprite到WebAV画布
      if (this.webavModule && newTimelineItem.sprite) {
        await this.webavModule.addSprite(newTimelineItem.sprite)
      }

      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItemData?.mediaItemId || '')
      console.log(`✅ 已复制统一时间轴项目: ${mediaItem?.name || '未知素材'}`)
    } catch (error) {
      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItemData?.mediaItemId || '')
      console.error(`❌ 复制时间轴项目失败: ${mediaItem?.name || '未知项目'}`, error)
      throw error
    }
  }

  /**
   * 撤销命令：删除复制的时间轴项目
   */
  async undo(): Promise<void> {
    try {
      console.log(`🔄 撤销复制操作：删除复制的时间轴项目...`)

      // 删除复制的时间轴项目
      this.timelineModule.removeTimelineItem(this.newTimelineItemId)

      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItemData?.mediaItemId || '')
      console.log(`↩️ 已撤销复制统一时间轴项目: ${mediaItem?.name || '未知素材'}`)
    } catch (error) {
      const mediaItem = this.mediaModule.getMediaItem(this.originalTimelineItemData?.mediaItemId || '')
      console.error(`❌ 撤销复制时间轴项目失败: ${mediaItem?.name || '未知项目'}`, error)
      throw error
    }
  }
}

// ==================== 更新变换属性命令 ====================

/**
 * 统一更新变换属性命令
 * 支持变换属性修改的撤销/重做操作
 * 适配新架构的统一类型系统
 */
export class UnifiedUpdateTransformCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string

  constructor(
    private timelineItemId: string,
    propertyType: 'position' | 'size' | 'rotation' | 'opacity' | 'zIndex' | 'multiple',
    private oldValues: TransformData,
    private newValues: TransformData,
    private timelineModule: {
      updateTimelineItemTransform: (id: string, transform: TransformData) => void
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    private mediaModule: {
      getMediaItem: (id: string) => UnifiedMediaItemData | undefined
    },
  ) {
    this.id = generateCommandId()

    const timelineItem = this.timelineModule.getTimelineItem(timelineItemId)
    const mediaItem = timelineItem
      ? this.mediaModule.getMediaItem(timelineItem.mediaItemId)
      : null

    // 生成描述信息
    this.description = this.generateDescription(mediaItem?.name || '未知素材')

    console.log('💾 保存变换属性操作数据:', {
      timelineItemId,
      propertyType,
      oldValues,
      newValues,
    })
  }

  /**
   * 生成命令描述
   */
  private generateDescription(mediaName: string): string {
    const changes: string[] = []

    // 检查位置变化
    if (
      (this.newValues.x !== undefined && this.oldValues.x !== undefined) ||
      (this.newValues.y !== undefined && this.oldValues.y !== undefined)
    ) {
      const oldX = this.oldValues.x ?? 0
      const oldY = this.oldValues.y ?? 0
      const newX = this.newValues.x ?? oldX
      const newY = this.newValues.y ?? oldY
      changes.push(
        `位置: (${oldX.toFixed(0)}, ${oldY.toFixed(0)}) → (${newX.toFixed(0)}, ${newY.toFixed(0)})`,
      )
    }

    // 检查大小变化
    if (
      (this.newValues.width !== undefined && this.oldValues.width !== undefined) ||
      (this.newValues.height !== undefined && this.oldValues.height !== undefined)
    ) {
      const oldWidth = this.oldValues.width ?? 0
      const oldHeight = this.oldValues.height ?? 0
      const newWidth = this.newValues.width ?? oldWidth
      const newHeight = this.newValues.height ?? oldHeight
      changes.push(
        `大小: ${oldWidth.toFixed(0)}×${oldHeight.toFixed(0)} → ${newWidth.toFixed(0)}×${newHeight.toFixed(0)}`,
      )
    }

    if (this.newValues.rotation !== undefined && this.oldValues.rotation !== undefined) {
      // 将弧度转换为角度显示
      const oldDegrees = ((this.oldValues.rotation * 180) / Math.PI).toFixed(1)
      const newDegrees = ((this.newValues.rotation * 180) / Math.PI).toFixed(1)
      changes.push(`旋转: ${oldDegrees}° → ${newDegrees}°`)
    }

    if (this.newValues.opacity !== undefined && this.oldValues.opacity !== undefined) {
      const oldOpacity = (this.oldValues.opacity * 100).toFixed(0)
      const newOpacity = (this.newValues.opacity * 100).toFixed(0)
      changes.push(`透明度: ${oldOpacity}% → ${newOpacity}%`)
    }

    if (this.newValues.zIndex !== undefined && this.oldValues.zIndex !== undefined) {
      changes.push(`层级: ${this.oldValues.zIndex} → ${this.newValues.zIndex}`)
    }

    const changeText = changes.length > 0 ? ` (${changes.join(', ')})` : ''
    return `更新变换属性: ${mediaName}${changeText}`
  }

  /**
   * 执行命令：应用新的变换属性
   */
  async execute(): Promise<void> {
    try {
      // 检查项目是否存在
      const timelineItem = this.timelineModule.getTimelineItem(this.timelineItemId)
      if (!timelineItem) {
        console.warn(`⚠️ 时间轴项目不存在，无法更新变换属性: ${this.timelineItemId}`)
        return
      }

      // 应用新的变换属性
      this.timelineModule.updateTimelineItemTransform(this.timelineItemId, this.newValues)

      const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
      console.log(`🔄 已更新变换属性: ${mediaItem?.name || '未知素材'}`)
    } catch (error) {
      const timelineItem = this.timelineModule.getTimelineItem(this.timelineItemId)
      let itemName = '未知素材'
      if (timelineItem) {
        const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
        itemName = mediaItem?.name || '未知素材'
      }
      console.error(`❌ 更新变换属性失败: ${itemName}`, error)
      throw error
    }
  }

  /**
   * 撤销命令：恢复到修改前的变换属性
   */
  async undo(): Promise<void> {
    try {
      // 检查项目是否存在
      const timelineItem = this.timelineModule.getTimelineItem(this.timelineItemId)
      if (!timelineItem) {
        console.warn(`⚠️ 时间轴项目不存在，无法撤销变换属性更新: ${this.timelineItemId}`)
        return
      }

      // 恢复旧的变换属性
      this.timelineModule.updateTimelineItemTransform(this.timelineItemId, this.oldValues)

      const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
      console.log(`↩️ 已撤销变换属性更新: ${mediaItem?.name || '未知素材'}`)
    } catch (error) {
      const timelineItem = this.timelineModule.getTimelineItem(this.timelineItemId)
      let itemName = '未知素材'
      if (timelineItem) {
        const mediaItem = this.mediaModule.getMediaItem(timelineItem.mediaItemId)
        itemName = mediaItem?.name || '未知素材'
      }
      console.error(`❌ 撤销变换属性更新失败: ${itemName}`, error)
      throw error
    }
  }
}

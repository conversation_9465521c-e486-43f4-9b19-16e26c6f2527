/**
 * 统一命令工厂函数
 * 提供便捷的命令创建方法，方便在新架构中创建各种命令
 * 适配新架构的统一类型系统
 */

import type { SimpleCommand } from '../../../types'
import type { UnifiedTimelineItemData, TransformData } from '../../timelineitem/TimelineItemData'
import type { UnifiedMediaItemData } from '../../mediaitem/types'
import type { Ref } from 'vue'

// 导入所有命令类
import {
  UnifiedSelectTimelineItemsCommand,
  UnifiedAddTimelineItemCommand,
  UnifiedRemoveTimelineItemCommand,
  UnifiedMoveTimelineItemCommand,
  UnifiedDuplicateTimelineItemCommand,
  UnifiedUpdateTransformCommand,
} from './unifiedTimelineCommands'

import {
  UnifiedBaseBatchCommand,
  UnifiedGenericBatchCommand,
  UnifiedBatchDeleteCommand,
  UnifiedBatchAutoArrangeTrackCommand,
  UnifiedBatchUpdatePropertiesCommand,
  UnifiedBatchSelectionCommand,
} from './unifiedBatchCommands'

import {
  UnifiedCreateKeyframeCommand,
  UnifiedDeleteKeyframeCommand,
  UnifiedUpdatePropertyCommand,
  UnifiedClearAllKeyframesCommand,
  UnifiedToggleKeyframeCommand,
} from './unifiedKeyframeCommands'

import {
  UnifiedAddTextItemCommand,
  UnifiedUpdateTextCommand,
  UnifiedRemoveTextItemCommand,
  UnifiedTextCommandFactory,
} from './unifiedTextCommands'

/**
 * 统一命令工厂类
 * 提供创建各种命令的便捷方法
 */
export class UnifiedCommandFactory {
  // ==================== 时间轴项目命令 ====================

  /**
   * 创建选择时间轴项目命令
   */
  static createSelectTimelineItemsCommand(
    itemIds: string[],
    mode: 'replace' | 'toggle',
    selectionModule: {
      selectedTimelineItemIds: Ref<Set<string>>
      selectTimelineItems: (itemIds: string[], mode: 'replace' | 'toggle') => void
      syncAVCanvasSelection: () => void
    },
    timelineModule: {
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    mediaModule: {
      getMediaItem: (id: string) => UnifiedMediaItemData | undefined
    },
  ): UnifiedSelectTimelineItemsCommand {
    return new UnifiedSelectTimelineItemsCommand(
      itemIds,
      mode,
      selectionModule,
      timelineModule,
      mediaModule,
    )
  }

  /**
   * 创建添加时间轴项目命令
   */
  static createAddTimelineItemCommand(
    timelineItem: UnifiedTimelineItemData,
    timelineModule: {
      addTimelineItem: (item: UnifiedTimelineItemData) => void
      removeTimelineItem: (id: string) => void
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    mediaModule: {
      getMediaItem: (id: string) => UnifiedMediaItemData | undefined
    },
    webavModule?: {
      addSprite: (sprite: any) => Promise<boolean>
      removeSprite: (sprite: any) => boolean
    },
  ): UnifiedAddTimelineItemCommand {
    return new UnifiedAddTimelineItemCommand(
      timelineItem,
      timelineModule,
      mediaModule,
      webavModule,
    )
  }

  /**
   * 创建移除时间轴项目命令
   */
  static createRemoveTimelineItemCommand(
    timelineItemId: string,
    timelineItem: UnifiedTimelineItemData,
    timelineModule: {
      addTimelineItem: (item: UnifiedTimelineItemData) => void
      removeTimelineItem: (id: string) => void
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    mediaModule: {
      getMediaItem: (id: string) => UnifiedMediaItemData | undefined
    },
    webavModule?: {
      addSprite: (sprite: any) => Promise<boolean>
      removeSprite: (sprite: any) => boolean
    },
  ): UnifiedRemoveTimelineItemCommand {
    return new UnifiedRemoveTimelineItemCommand(
      timelineItemId,
      timelineItem,
      timelineModule,
      mediaModule,
      webavModule,
    )
  }

  /**
   * 创建移动时间轴项目命令
   */
  static createMoveTimelineItemCommand(
    timelineItemId: string,
    oldPositionFrames: number,
    newPositionFrames: number,
    oldTrackId: string,
    newTrackId: string,
    timelineModule: {
      updateTimelineItemPosition: (id: string, positionFrames: number, trackId?: string) => void
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    mediaModule: {
      getMediaItem: (id: string) => UnifiedMediaItemData | undefined
    },
  ): UnifiedMoveTimelineItemCommand {
    return new UnifiedMoveTimelineItemCommand(
      timelineItemId,
      oldPositionFrames,
      newPositionFrames,
      oldTrackId,
      newTrackId,
      timelineModule,
      mediaModule,
    )
  }

  /**
   * 创建复制时间轴项目命令
   */
  static createDuplicateTimelineItemCommand(
    originalTimelineItemId: string,
    originalTimelineItem: UnifiedTimelineItemData,
    newPositionFrames: number,
    timelineModule: {
      addTimelineItem: (item: UnifiedTimelineItemData) => void
      removeTimelineItem: (id: string) => void
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    mediaModule: {
      getMediaItem: (id: string) => UnifiedMediaItemData | undefined
    },
    webavModule?: {
      addSprite: (sprite: any) => Promise<boolean>
      removeSprite: (sprite: any) => boolean
    },
  ): UnifiedDuplicateTimelineItemCommand {
    return new UnifiedDuplicateTimelineItemCommand(
      originalTimelineItemId,
      originalTimelineItem,
      newPositionFrames,
      timelineModule,
      mediaModule,
      webavModule,
    )
  }

  /**
   * 创建更新变换属性命令
   */
  static createUpdateTransformCommand(
    timelineItemId: string,
    propertyType: 'position' | 'size' | 'rotation' | 'opacity' | 'zIndex' | 'multiple',
    oldValues: TransformData,
    newValues: TransformData,
    timelineModule: {
      updateTimelineItemTransform: (id: string, transform: TransformData) => void
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    mediaModule: {
      getMediaItem: (id: string) => UnifiedMediaItemData | undefined
    },
  ): UnifiedUpdateTransformCommand {
    return new UnifiedUpdateTransformCommand(
      timelineItemId,
      propertyType,
      oldValues,
      newValues,
      timelineModule,
      mediaModule,
    )
  }

  // ==================== 批量命令 ====================

  /**
   * 创建通用批量命令
   */
  static createGenericBatchCommand(
    description: string,
    commands: SimpleCommand[],
  ): UnifiedGenericBatchCommand {
    return new UnifiedGenericBatchCommand(description, commands)
  }

  /**
   * 创建批量删除命令
   */
  static createBatchDeleteCommand(
    timelineItemIds: string[],
    timelineModule: {
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
      addTimelineItem: (item: UnifiedTimelineItemData) => void
      removeTimelineItem: (id: string) => void
    },
    mediaModule: {
      getMediaItem: (id: string) => UnifiedMediaItemData | undefined
    },
    webavModule?: {
      addSprite: (sprite: any) => Promise<boolean>
      removeSprite: (sprite: any) => boolean
    },
  ): UnifiedBatchDeleteCommand {
    return new UnifiedBatchDeleteCommand(
      timelineItemIds,
      timelineModule,
      mediaModule,
      webavModule,
    )
  }

  /**
   * 创建批量自动排列轨道命令
   */
  static createBatchAutoArrangeTrackCommand(
    trackId: string,
    timelineItems: UnifiedTimelineItemData[],
    timelineModule: {
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
      updateTimelineItemPosition: (id: string, position: number, trackId?: string) => void
    },
    mediaModule: {
      getMediaItem: (id: string) => UnifiedMediaItemData | undefined
    },
    trackModule: {
      getTrack: (trackId: string) => any | undefined
    },
  ): UnifiedBatchAutoArrangeTrackCommand {
    return new UnifiedBatchAutoArrangeTrackCommand(
      trackId,
      timelineItems,
      timelineModule,
      mediaModule,
      trackModule,
    )
  }

  /**
   * 创建批量属性更新命令
   */
  static createBatchUpdatePropertiesCommand(
    targetItemIds: string[],
    updateCommands: SimpleCommand[],
  ): UnifiedBatchUpdatePropertiesCommand {
    return new UnifiedBatchUpdatePropertiesCommand(targetItemIds, updateCommands)
  }

  /**
   * 创建批量选择命令
   */
  static createBatchSelectionCommand(
    itemIds: string[],
    mode: 'select' | 'deselect' | 'toggle',
    selectionModule: {
      selectTimelineItems: (itemIds: string[], mode: 'replace' | 'toggle') => void
      selectedTimelineItemIds: { value: Set<string> }
    },
  ): UnifiedBatchSelectionCommand {
    return new UnifiedBatchSelectionCommand(itemIds, mode, selectionModule)
  }

  // ==================== 关键帧命令 ====================

  /**
   * 创建关键帧命令
   */
  static createKeyframeCommand(
    timelineItemId: string,
    frame: number,
    timelineModule: {
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    webavAnimationManager: {
      updateWebAVAnimation: (item: UnifiedTimelineItemData) => Promise<void>
    },
    playbackControls?: {
      seekTo: (frame: number) => void
    },
  ): UnifiedCreateKeyframeCommand {
    return new UnifiedCreateKeyframeCommand(
      timelineItemId,
      frame,
      timelineModule,
      webavAnimationManager,
      playbackControls,
    )
  }

  /**
   * 创建删除关键帧命令
   */
  static createDeleteKeyframeCommand(
    timelineItemId: string,
    frame: number,
    timelineModule: {
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    webavAnimationManager: {
      updateWebAVAnimation: (item: UnifiedTimelineItemData) => Promise<void>
    },
    playbackControls?: {
      seekTo: (frame: number) => void
    },
  ): UnifiedDeleteKeyframeCommand {
    return new UnifiedDeleteKeyframeCommand(
      timelineItemId,
      frame,
      timelineModule,
      webavAnimationManager,
      playbackControls,
    )
  }

  // ==================== 文本命令 ====================

  /**
   * 获取文本命令工厂
   */
  static get TextCommands() {
    return UnifiedTextCommandFactory
  }
}

/**
 * 导出所有命令类和工厂函数
 */
export {
  // 时间轴命令
  UnifiedSelectTimelineItemsCommand,
  UnifiedAddTimelineItemCommand,
  UnifiedRemoveTimelineItemCommand,
  UnifiedMoveTimelineItemCommand,
  UnifiedDuplicateTimelineItemCommand,
  UnifiedUpdateTransformCommand,
  
  // 批量命令
  UnifiedBaseBatchCommand,
  UnifiedGenericBatchCommand,
  UnifiedBatchDeleteCommand,
  UnifiedBatchAutoArrangeTrackCommand,
  UnifiedBatchUpdatePropertiesCommand,
  UnifiedBatchSelectionCommand,
  
  // 关键帧命令
  UnifiedCreateKeyframeCommand,
  UnifiedDeleteKeyframeCommand,
  UnifiedUpdatePropertyCommand,
  UnifiedClearAllKeyframesCommand,
  UnifiedToggleKeyframeCommand,
  
  // 文本命令
  UnifiedAddTextItemCommand,
  UnifiedUpdateTextCommand,
  UnifiedRemoveTextItemCommand,
  UnifiedTextCommandFactory,
}

import { generateCommandId } from '../../../utils/idGenerator'
import type { SimpleCommand } from '../../../types'
import type { UnifiedTimelineItemData } from '../../timelineitem/TimelineItemData'
import type { UnifiedMediaItemData } from '../../mediaitem/types'

/**
 * 统一文本相关的命令实现
 * 提供文本项目的创建、更新、删除等操作的撤销/重做支持
 * 适配新架构的统一类型系统
 */

/**
 * 统一添加文本项目命令
 * 支持撤销/重做的文本项目创建操作
 * 适配新架构的统一时间轴项目系统
 */
export class UnifiedAddTextItemCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string
  private textItem: UnifiedTimelineItemData | null = null

  constructor(
    private text: string,
    private style: any, // 暂时使用any，后续需要定义具体的文本样式类型
    private startTimeFrames: number,
    private trackId: string,
    private duration: number,
    private videoResolution: { width: number; height: number },
    private timelineModule: {
      addTimelineItem: (item: UnifiedTimelineItemData) => void
      removeTimelineItem: (id: string) => void
    },
    private webavModule?: {
      addSprite: (sprite: any) => Promise<boolean>
      removeSprite: (sprite: any) => boolean
    }
  ) {
    this.id = generateCommandId()
    this.description = `添加文本: ${text.substring(0, 10)}${text.length > 10 ? '...' : ''}`
  }

  async execute(): Promise<void> {
    try {
      console.log(`🔄 [UnifiedAddTextItemCommand] 执行添加文本操作...`)

      // 在新架构中创建文本时间轴项目
      // 这里需要根据新架构的具体实现来调整
      this.textItem = {
        id: `text_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        mediaItemId: '', // 文本项目可能不需要媒体项目ID
        trackId: this.trackId,
        timelineStatus: 'ready',
        mediaType: 'text',
        timeRange: {
          timelineStartTime: this.startTimeFrames,
          timelineEndTime: this.startTimeFrames + this.duration,
        },
        config: {
          name: this.text,
          transform: {
            x: this.videoResolution.width / 2,
            y: this.videoResolution.height / 2,
            width: 200,
            height: 50,
            rotation: 0,
            opacity: 1,
            zIndex: 1,
          },
          // 文本特定配置
          text: this.text,
          style: this.style,
        },
        sprite: undefined, // 新架构中sprite的管理方式可能不同
      }

      // 1. 添加到时间轴
      this.timelineModule.addTimelineItem(this.textItem)

      // 2. 如果有webav模块，添加sprite到WebAV画布
      if (this.webavModule && this.textItem.sprite) {
        await this.webavModule.addSprite(this.textItem.sprite)
      }

      console.log(`✅ [UnifiedAddTextItemCommand] 文本项目添加成功:`, {
        id: this.textItem.id,
        text: this.text.substring(0, 20) + '...',
        startTime: this.startTimeFrames,
        duration: this.duration
      })
    } catch (error) {
      console.error(`❌ [UnifiedAddTextItemCommand] 添加文本项目失败:`, error)
      throw error
    }
  }

  async undo(): Promise<void> {
    try {
      if (this.textItem) {
        console.log(`🔄 [UnifiedAddTextItemCommand] 撤销添加文本操作...`)

        // 1. 如果有webav模块，从WebAV画布移除sprite
        if (this.webavModule && this.textItem.sprite) {
          this.webavModule.removeSprite(this.textItem.sprite)
        }

        // 2. 从时间轴移除项目
        this.timelineModule.removeTimelineItem(this.textItem.id)

        console.log(`✅ [UnifiedAddTextItemCommand] 文本项目撤销成功: ${this.textItem.id}`)
      }
    } catch (error) {
      console.error(`❌ [UnifiedAddTextItemCommand] 撤销文本项目失败:`, error)
      throw error
    }
  }
}

/**
 * 统一更新文本内容命令
 * 支持撤销/重做的文本内容和样式更新操作
 * 适配新架构的统一时间轴项目系统
 */
export class UnifiedUpdateTextCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string
  private oldText: string = ''
  private oldStyle: any = null

  constructor(
    private timelineItemId: string,
    private newText: string,
    private newStyle: any, // 暂时使用any，后续需要定义具体的文本样式类型
    private timelineModule: {
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    }
  ) {
    this.id = generateCommandId()
    this.description = `更新文本: ${newText.substring(0, 10)}${newText.length > 10 ? '...' : ''}`
  }

  async execute(): Promise<void> {
    try {
      console.log(`🔄 [UnifiedUpdateTextCommand] 执行更新文本操作...`)

      const item = this.timelineModule.getTimelineItem(this.timelineItemId)
      if (!item || item.mediaType !== 'text') {
        throw new Error(`文本项目不存在或类型错误: ${this.timelineItemId}`)
      }

      // 保存旧值用于撤销
      const config = item.config as any
      this.oldText = config.text || ''
      this.oldStyle = config.style ? { ...config.style } : null

      // 更新文本内容和样式
      config.text = this.newText
      if (this.newStyle) {
        config.style = { ...config.style, ...this.newStyle }
      }

      // 在新架构中，可能需要重新创建sprite或触发更新
      // 这里需要根据新架构的具体实现来调整

      console.log(`✅ [UnifiedUpdateTextCommand] 文本更新成功:`, {
        id: this.timelineItemId,
        oldText: this.oldText.substring(0, 20) + '...',
        newText: this.newText.substring(0, 20) + '...'
      })
    } catch (error) {
      console.error(`❌ [UnifiedUpdateTextCommand] 更新文本失败:`, error)
      throw error
    }
  }

  async undo(): Promise<void> {
    try {
      if (this.oldText !== '') {
        console.log(`🔄 [UnifiedUpdateTextCommand] 撤销更新文本操作...`)

        const item = this.timelineModule.getTimelineItem(this.timelineItemId)
        if (!item || item.mediaType !== 'text') {
          throw new Error(`文本项目不存在或类型错误: ${this.timelineItemId}`)
        }

        // 恢复旧的文本内容和样式
        const config = item.config as any
        config.text = this.oldText
        if (this.oldStyle) {
          config.style = { ...this.oldStyle }
        }

        // 在新架构中，可能需要重新创建sprite或触发更新
        // 这里需要根据新架构的具体实现来调整

        console.log(`✅ [UnifiedUpdateTextCommand] 文本撤销成功: ${this.timelineItemId}`)
      }
    } catch (error) {
      console.error(`❌ [UnifiedUpdateTextCommand] 撤销文本更新失败:`, error)
      throw error
    }
  }
}

/**
 * 统一删除文本项目命令
 * 支持撤销/重做的文本项目删除操作
 * 适配新架构的统一时间轴项目系统
 */
export class UnifiedRemoveTextItemCommand implements SimpleCommand {
  public readonly id: string
  public readonly description: string
  private removedItem: UnifiedTimelineItemData | null = null

  constructor(
    private timelineItemId: string,
    private timelineModule: {
      addTimelineItem: (item: UnifiedTimelineItemData) => void
      removeTimelineItem: (id: string) => void
      getTimelineItem: (id: string) => UnifiedTimelineItemData | undefined
    },
    private webavModule?: {
      addSprite: (sprite: any) => Promise<boolean>
      removeSprite: (sprite: any) => boolean
    }
  ) {
    this.id = generateCommandId()
    this.description = `删除文本项目`
  }

  async execute(): Promise<void> {
    try {
      console.log(`🔄 [UnifiedRemoveTextItemCommand] 执行删除文本操作...`)

      const item = this.timelineModule.getTimelineItem(this.timelineItemId)
      if (!item || item.mediaType !== 'text') {
        throw new Error(`文本项目不存在或类型错误: ${this.timelineItemId}`)
      }

      // 保存项目用于撤销
      this.removedItem = { ...item }

      // 1. 如果有webav模块，从WebAV画布移除sprite
      if (this.webavModule && item.sprite) {
        this.webavModule.removeSprite(item.sprite)
      }

      // 2. 从时间轴移除项目
      this.timelineModule.removeTimelineItem(this.timelineItemId)

      console.log(`✅ [UnifiedRemoveTextItemCommand] 文本项目删除成功: ${this.timelineItemId}`)
    } catch (error) {
      console.error(`❌ [UnifiedRemoveTextItemCommand] 删除文本项目失败:`, error)
      throw error
    }
  }

  async undo(): Promise<void> {
    try {
      if (this.removedItem) {
        console.log(`🔄 [UnifiedRemoveTextItemCommand] 撤销删除文本操作...`)

        // 1. 重新添加到时间轴
        this.timelineModule.addTimelineItem(this.removedItem)

        // 2. 如果有webav模块，重新添加sprite到WebAV画布
        if (this.webavModule && this.removedItem.sprite) {
          await this.webavModule.addSprite(this.removedItem.sprite)
        }

        console.log(`✅ [UnifiedRemoveTextItemCommand] 文本项目恢复成功: ${this.removedItem.id}`)
      }
    } catch (error) {
      console.error(`❌ [UnifiedRemoveTextItemCommand] 撤销删除文本项目失败:`, error)
      throw error
    }
  }
}

/**
 * 统一文本命令工厂函数
 * 提供便捷的命令创建方法
 * 适配新架构的统一类型系统
 */
export const UnifiedTextCommandFactory = {
  /**
   * 创建添加文本项目命令
   */
  createAddTextCommand(
    text: string,
    style: any,
    startTimeFrames: number,
    trackId: string,
    duration: number,
    videoResolution: { width: number; height: number },
    timelineModule: any,
    webavModule?: any
  ): UnifiedAddTextItemCommand {
    return new UnifiedAddTextItemCommand(
      text,
      style,
      startTimeFrames,
      trackId,
      duration,
      videoResolution,
      timelineModule,
      webavModule
    )
  },

  /**
   * 创建更新文本命令
   */
  createUpdateTextCommand(
    timelineItemId: string,
    newText: string,
    newStyle: any,
    timelineModule: any
  ): UnifiedUpdateTextCommand {
    return new UnifiedUpdateTextCommand(timelineItemId, newText, newStyle, timelineModule)
  },

  /**
   * 创建删除文本项目命令
   */
  createRemoveTextCommand(
    timelineItemId: string,
    timelineModule: any,
    webavModule?: any
  ): UnifiedRemoveTextItemCommand {
    return new UnifiedRemoveTextItemCommand(timelineItemId, timelineModule, webavModule)
  }
}

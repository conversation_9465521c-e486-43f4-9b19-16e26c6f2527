# 统一命令模块 (Unified Commands Module)

本模块为新架构的统一视频编辑器提供完整的命令系统，支持撤销/重做功能。

## 架构概述

新架构的命令系统基于旧架构进行了重新设计，适配了统一的类型系统和模块化结构：

- **统一类型系统**: 使用 `UnifiedTimelineItemData`、`UnifiedMediaItemData` 等统一类型
- **模块化设计**: 将命令按功能分类到不同的文件中
- **工厂模式**: 提供统一的命令工厂函数，简化命令创建
- **完整的撤销/重做支持**: 所有命令都实现了 `SimpleCommand` 接口

## 文件结构

```
commands/
├── index.ts                      # 模块导出索引
├── UnifiedCommandFactory.ts      # 统一命令工厂
├── unifiedTimelineCommands.ts    # 时间轴相关命令
├── unifiedBatchCommands.ts       # 批量操作命令
├── unifiedKeyframeCommands.ts    # 关键帧相关命令
├── unifiedTextCommands.ts        # 文本相关命令
└── README.md                     # 本文档
```

## 命令分类

### 1. 时间轴命令 (Timeline Commands)

处理时间轴项目的基本操作：

- `UnifiedSelectTimelineItemsCommand` - 选择时间轴项目
- `UnifiedAddTimelineItemCommand` - 添加时间轴项目
- `UnifiedRemoveTimelineItemCommand` - 移除时间轴项目
- `UnifiedMoveTimelineItemCommand` - 移动时间轴项目
- `UnifiedDuplicateTimelineItemCommand` - 复制时间轴项目
- `UnifiedUpdateTransformCommand` - 更新变换属性

### 2. 批量命令 (Batch Commands)

处理批量操作，将多个单个命令组合为一个批量操作：

- `UnifiedBaseBatchCommand` - 批量命令基类
- `UnifiedGenericBatchCommand` - 通用批量命令
- `UnifiedBatchDeleteCommand` - 批量删除命令
- `UnifiedBatchAutoArrangeTrackCommand` - 批量自动排列轨道命令
- `UnifiedBatchUpdatePropertiesCommand` - 批量属性修改命令
- `UnifiedBatchSelectionCommand` - 批量选择命令

### 3. 关键帧命令 (Keyframe Commands)

处理关键帧和动画相关操作：

- `UnifiedCreateKeyframeCommand` - 创建关键帧
- `UnifiedDeleteKeyframeCommand` - 删除关键帧
- `UnifiedUpdatePropertyCommand` - 更新属性（智能处理关键帧）
- `UnifiedClearAllKeyframesCommand` - 清除所有关键帧
- `UnifiedToggleKeyframeCommand` - 切换关键帧

### 4. 文本命令 (Text Commands)

处理文本项目的特殊操作：

- `UnifiedAddTextItemCommand` - 添加文本项目
- `UnifiedUpdateTextCommand` - 更新文本内容和样式
- `UnifiedRemoveTextItemCommand` - 删除文本项目

## 使用方法

### 推荐方式：使用工厂函数

```typescript
import { UnifiedCommandFactory } from '@/unified/modules/commands'

// 创建选择命令
const selectCommand = UnifiedCommandFactory.createSelectTimelineItemsCommand(
  ['item1', 'item2'],
  'replace',
  selectionModule,
  timelineModule,
  mediaModule
)

// 执行命令
await selectCommand.execute()

// 撤销命令
await selectCommand.undo()
```

### 直接使用命令类

```typescript
import { UnifiedSelectTimelineItemsCommand } from '@/unified/modules/commands'

const selectCommand = new UnifiedSelectTimelineItemsCommand(
  ['item1', 'item2'],
  'replace',
  selectionModule,
  timelineModule,
  mediaModule
)

await selectCommand.execute()
await selectCommand.undo()
```

### 批量操作示例

```typescript
import { UnifiedCommandFactory } from '@/unified/modules/commands'

// 创建多个单个命令
const commands = [
  UnifiedCommandFactory.createMoveTimelineItemCommand(...),
  UnifiedCommandFactory.createUpdateTransformCommand(...),
  // ... 更多命令
]

// 创建批量命令
const batchCommand = UnifiedCommandFactory.createGenericBatchCommand(
  '批量操作',
  commands
)

// 执行批量操作
await batchCommand.execute()

// 撤销批量操作（会逆序撤销所有子命令）
await batchCommand.undo()
```

## 与旧架构的对比

### 主要改进

1. **统一类型系统**: 使用新架构的统一类型，避免了类型不一致的问题
2. **模块化设计**: 按功能分类，代码更清晰易维护
3. **工厂模式**: 简化了命令创建过程，减少了重复代码
4. **更好的错误处理**: 统一的错误处理和日志记录
5. **完整的文档**: 每个命令都有详细的注释和使用说明

### 迁移指南

从旧架构迁移到新架构时，主要需要注意：

1. **类型更新**: 将 `TimelineItemData` 替换为 `UnifiedTimelineItemData`
2. **模块引用**: 更新导入路径到新的统一模块
3. **工厂函数**: 推荐使用工厂函数创建命令，而不是直接实例化
4. **接口适配**: 确保传入的模块接口符合新架构的要求

## 扩展指南

### 添加新命令

1. 在相应的命令文件中添加新的命令类
2. 实现 `SimpleCommand` 接口的 `execute()` 和 `undo()` 方法
3. 在 `UnifiedCommandFactory` 中添加对应的工厂方法
4. 在 `index.ts` 中导出新的命令类

### 命令设计原则

1. **单一职责**: 每个命令只负责一个特定的操作
2. **可逆性**: 所有命令都必须支持撤销操作
3. **状态保存**: 在执行前保存必要的状态信息用于撤销
4. **错误处理**: 提供完整的错误处理和日志记录
5. **性能考虑**: 避免在命令中进行重复的计算或查询

## 注意事项

1. **异步操作**: 所有命令的 `execute()` 和 `undo()` 方法都是异步的
2. **状态一致性**: 确保命令执行后系统状态的一致性
3. **内存管理**: 注意避免在命令中保存大量数据导致内存泄漏
4. **循环依赖**: 使用动态导入避免模块间的循环依赖
5. **WebAV集成**: 部分命令需要与WebAV模块集成，注意处理sprite的生命周期

## 未来计划

1. **性能优化**: 优化批量操作的性能
2. **更多命令类型**: 添加更多专业的视频编辑命令
3. **命令宏**: 支持录制和回放命令序列
4. **智能撤销**: 基于上下文的智能撤销建议
5. **命令历史分析**: 提供命令使用统计和分析功能
